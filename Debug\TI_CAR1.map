******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 16:17:55 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000077b9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009c98  00016368  R  X
  SRAM                  20200000   00008000  00000706  000078fa  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009c98   00009c98    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008470   00008470    r-x .text
  00008530    00008530    000016f0   000016f0    r-- .rodata
  00009c20    00009c20    00000078   00000078    r-- .cinit
20200000    20200000    00000507   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000133   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008470     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001fe4    000001d4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000021b8    000001b0     Task.o (.text.Task_Start)
                  00002368    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002508    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000269a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000269c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002824    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029ac    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b24    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002c94    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002dd8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002f14    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003048    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000317c    00000130     OLED.o (.text.OLED_ShowChar)
                  000032ac    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000033dc    00000128     Task_App.o (.text.Task_Tracker)
                  00003504    00000128     inv_mpu.o (.text.mpu_init)
                  0000362c    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003750    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003874    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003994    00000110     OLED.o (.text.OLED_Init)
                  00003aa4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003bb0    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003cb8    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003db8    000000fc     Task_App.o (.text.Task_Init)
                  00003eb4    000000f0     Motor.o (.text.Motor_SetDirc)
                  00003fa4    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00004094    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004180    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004264    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004348    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  0000442c    000000e0     Task_App.o (.text.Task_OLED)
                  0000450c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000045e8    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000046c4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000479c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004874    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004948    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004a18    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00004adc    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004ba0    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004c64    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004d20    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004dd8    000000b4     Task.o (.text.Task_Add)
                  00004e8c    000000ac     Task_App.o (.text.Task_Serial)
                  00004f38    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004fe4    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005090    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000513a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000513c    000000a2                            : udivmoddi4.S.obj (.text)
                  000051de    00000002     --HOLE-- [fill = 0]
                  000051e0    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005280    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005320    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  000053bc    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005454    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000054ec    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005582    00000002     --HOLE-- [fill = 0]
                  00005584    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005610    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  0000569c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005720    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000057a4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005826    00000002     --HOLE-- [fill = 0]
                  00005828    00000080     Motor.o (.text.Motor_GetSpeed)
                  000058a8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00005928    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000059a4    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005a18    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005a20    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005a94    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005b08    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005b7a    00000002     --HOLE-- [fill = 0]
                  00005b7c    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005bec    0000006e     OLED.o (.text.OLED_ShowString)
                  00005c5a    00000002     --HOLE-- [fill = 0]
                  00005c5c    0000006c     Motor.o (.text.Motor_Start)
                  00005cc8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005d34    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005d9e    00000002     --HOLE-- [fill = 0]
                  00005da0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005e08    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005e6e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005ed4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005f38    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005f9c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005ffe    00000002     --HOLE-- [fill = 0]
                  00006000    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00006062    00000002     --HOLE-- [fill = 0]
                  00006064    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000060c4    00000060     Key_Led.o (.text.Key_Read)
                  00006124    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006184    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  000061e4    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006244    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000062a4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006302    00000002     --HOLE-- [fill = 0]
                  00006304    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006360    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000063bc    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006414    00000058     Serial.o (.text.Serial_Init)
                  0000646c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000064c4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000651c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006572    00000002     --HOLE-- [fill = 0]
                  00006574    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  000065c8    00000054     OLED.o (.text.mspm0_i2c_enable)
                  0000661c    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000666e    00000002     --HOLE-- [fill = 0]
                  00006670    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000066c0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006710    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006760    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000067ac    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000067f8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006844    0000004c     OLED.o (.text.OLED_Printf)
                  00006890    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000068dc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006926    00000002     --HOLE-- [fill = 0]
                  00006928    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006972    00000002     --HOLE-- [fill = 0]
                  00006974    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000069bc    00000048     ADC.o (.text.adc_getValue)
                  00006a04    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006a4c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006a94    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006ad8    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006b1c    00000044     Task_App.o (.text.Task_Key)
                  00006b60    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006ba4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006be8    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006c2c    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006c70    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00006cb2    00000002     --HOLE-- [fill = 0]
                  00006cb4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006cf6    00000002     --HOLE-- [fill = 0]
                  00006cf8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00006d38    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006d78    00000040     Task_App.o (.text.Task_GraySensor)
                  00006db8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006df8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006e38    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006e78    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006eb8    0000003e     Task.o (.text.Task_CMP)
                  00006ef6    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006f34    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006f70    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006fac    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006fe8    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00007024    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00007060    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  0000709c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000070d8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007114    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007150    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000718a    00000002     --HOLE-- [fill = 0]
                  0000718c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000071c6    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  000071fe    00000002     --HOLE-- [fill = 0]
                  00007200    00000038     Task_App.o (.text.Task_LED)
                  00007238    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00007270    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000072a4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000072d8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000730c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007340    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00007372    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000073a4    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  000073d4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007404    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007434    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007464    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007494    00000030            : vsnprintf.c.obj (.text._outs)
                  000074c4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000074f4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007524    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007550    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  0000757c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000075a8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  000075d4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000075fe    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007626    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000764e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007676    00000002     --HOLE-- [fill = 0]
                  00007678    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000076a0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000076c8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000076f0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007718    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007740    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007768    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007790    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000077b8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000077e0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007806    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000782c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007852    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007878    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000789c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000078c0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000078e4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007906    00000002     --HOLE-- [fill = 0]
                  00007908    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007928    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007948    00000020     SysTick.o (.text.Delay)
                  00007968    00000020     main.o (.text.main)
                  00007988    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000079a8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000079c6    00000002     --HOLE-- [fill = 0]
                  000079c8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000079e6    00000002     --HOLE-- [fill = 0]
                  000079e8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007a04    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007a20    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007a3c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007a58    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007a74    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007a90    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007aac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007ac8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007ae4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007b00    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007b1c    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007b38    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007b54    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007b70    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007b8c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007ba8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007bc4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007be0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007bfc    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007c18    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007c34    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007c4c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007c64    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007c7c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007c94    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007cac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007cc4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007cdc    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007cf4    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007d0c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007d24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007d3c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007d54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007d6c    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007d84    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007d9c    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00007db4    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007dcc    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007de4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007dfc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007e14    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007e2c    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007e44    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007e5c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007e74    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007e8c    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007ea4    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007ebc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007ed4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007eec    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007f04    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007f1c    00000018     OLED.o (.text.DL_I2C_reset)
                  00007f34    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007f4c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007f64    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007f7c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007f94    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007fac    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007fc4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007fdc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007ff4    00000018     Motor.o (.text.DL_Timer_startCounter)
                  0000800c    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008024    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000803c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008054    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000806c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008084    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000809c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000080b4    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000080cc    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000080e4    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000080fc    00000018            : vsprintf.c.obj (.text._outs)
                  00008114    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000812a    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00008140    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00008156    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  0000816c    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00008182    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008198    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000081ae    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000081c4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000081da    00000016     SysTick.o (.text.SysGetTick)
                  000081f0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00008206    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000821a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000822e    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00008242    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00008256    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000826a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000827e    00000002     --HOLE-- [fill = 0]
                  00008280    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008294    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000082a8    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000082bc    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000082d0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000082e4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000082f8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000830c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00008320    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008334    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00008348    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000835c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000836e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00008380    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00008392    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000083a2    00000002     --HOLE-- [fill = 0]
                  000083a4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000083b4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000083c4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000083d4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000083e4    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000083f2    00000002     --HOLE-- [fill = 0]
                  000083f4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00008402    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00008410    0000000e     MPU6050.o (.text.tap_cb)
                  0000841e    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  0000842c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008438    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008444    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000844e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008458    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008468    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008472    00000002     --HOLE-- [fill = 0]
                  00008474    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008484    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000848e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008498    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000084a2    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000084ac    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000084bc    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  000084c6    0000000a     MPU6050.o (.text.android_orient_cb)
                  000084d0    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000084d8    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000084e0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000084e8    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000084ee    00000002     --HOLE-- [fill = 0]
                  000084f0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008500    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008506    00000006            : exit.c.obj (.text:abort)
                  0000850c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008510    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008514    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008518    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000851c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000852c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00009c20    00000078     
                  00009c20    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00009c6e    00000002     --HOLE-- [fill = 0]
                  00009c70    0000000c     (__TI_handler_table)
                  00009c7c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009c84    00000010     (__TI_cinit_table)
                  00009c94    00000004     --HOLE-- [fill = 0]

.rodata    0    00008530    000016f0     
                  00008530    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009126    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009716    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000993e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009940    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009a41    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009a48    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009a88    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009ab0    00000028     inv_mpu.o (.rodata.test)
                  00009ad8    0000001f     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009af7    0000001e     inv_mpu.o (.rodata.reg)
                  00009b15    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00009b18    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009b30    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009b48    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009b5c    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009b70    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009b84    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009b95    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009ba6    00000011     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009bb7    00000001     --HOLE-- [fill = 0]
                  00009bb8    0000000c     inv_mpu.o (.rodata.hw)
                  00009bc4    0000000c     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009bd0    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009bdb    00000001     --HOLE-- [fill = 0]
                  00009bdc    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009be6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009be8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00009bf0    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00009bf8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009c00    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009c06    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009c0b    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009c0f    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009c13    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009c15    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000133     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200503    00000001     Task_App.o (.data.Gray_Digtal)
                  20200504    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200505    00000001     Task.o (.data.Task_Num)
                  20200506    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3342    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3382    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1512    165       241    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1982    165       247    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2456    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1846    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8190    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     33852   6165      1798   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009c84 records: 2, size/record: 8, table size: 16
	.data: load addr=00009c20, load size=0000004e bytes, run addr=202003d4, run size=00000133 bytes, compression=lzss
	.bss: load addr=00009c7c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009c70 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002509     00008458     00008456   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004265     00008474     00008470   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000848c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000084a0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000084d6          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008504          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003aa5     000084ac     000084aa   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002513     000084f0     000084ec   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008516          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000077b9     0000851c     00008518   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000850d  ADC0_IRQHandler                      
0000850d  ADC1_IRQHandler                      
0000850d  AES_IRQHandler                       
00008510  C$$EXIT                              
0000850d  CANFD0_IRQHandler                    
0000850d  DAC0_IRQHandler                      
00006cf9  DL_ADC12_setClockConfig              
00008445  DL_Common_delayCycles                
000067ad  DL_DMA_initChannel                   
000062a5  DL_I2C_fillControllerTXFIFO          
00006fe9  DL_I2C_flushControllerTXFIFO         
00007853  DL_I2C_setClockConfig                
0000450d  DL_SYSCTL_configSYSPLL               
00005ed5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006a95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004a19  DL_Timer_initPWMMode                 
00007bc5  DL_Timer_setCaptCompUpdateMethod     
00007fdd  DL_Timer_setCaptureCompareOutCtl     
000083b5  DL_Timer_setCaptureCompareValue      
00007be1  DL_Timer_setClockConfig              
00006975  DL_UART_init                         
0000835d  DL_UART_setClockConfig               
0000850d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
0000850d  Default_Handler                      
00007949  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200502  Flag_MPU6050_Ready                   
0000850d  GROUP0_IRQHandler                    
00004181  GROUP1_IRQHandler                    
000045e9  Get_Analog_value                     
00007025  Get_Anolog_Value                     
000083e5  Get_Digtal_For_User                  
000071c7  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200503  Gray_Digtal                          
202004a0  Gray_Normal                          
00008511  HOSTexit                             
0000850d  HardFault_Handler                    
0000850d  I2C0_IRQHandler                      
0000850d  I2C1_IRQHandler                      
00005d35  I2C_OLED_Clear                       
00007061  I2C_OLED_Set_Pos                     
000053bd  I2C_OLED_WR_Byte                     
00006065  I2C_OLED_i2c_sda_unlock              
00006d39  Interrupt_Init                       
000060c5  Key_Read                             
00002c95  MPU6050_Init                         
202004e0  Motor                                
00005829  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
000051e1  Motor_SetDuty                        
00005c5d  Motor_Start                          
00005b7d  MyPrintf_DMA                         
0000850d  NMI_Handler                          
0000269d  No_MCU_Ganv_Sensor_Init              
00005b09  No_MCU_Ganv_Sensor_Init_Frist        
00006c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003995  OLED_Init                            
00006845  OLED_Printf                          
0000317d  OLED_ShowChar                        
00005bed  OLED_ShowString                      
000075d5  PID_IQ_Init                          
0000362d  PID_IQ_Prosc                         
00006ad9  PID_IQ_SetParams                     
0000850d  PendSV_Handler                       
0000850d  RTC_IRQHandler                       
0000159d  Read_Quad                            
00008519  Reset_Handler                        
0000850d  SPI0_IRQHandler                      
0000850d  SPI1_IRQHandler                      
0000850d  SVC_Handler                          
00006891  SYSCFG_DL_ADC1_init                  
00007405  SYSCFG_DL_DMA_CH_RX_init             
0000809d  SYSCFG_DL_DMA_CH_TX_init             
0000842d  SYSCFG_DL_DMA_init                   
00001fe5  SYSCFG_DL_GPIO_init                  
000063bd  SYSCFG_DL_I2C_MPU6050_init           
00005f39  SYSCFG_DL_I2C_OLED_init              
000058a9  SYSCFG_DL_Motor_PWM_init             
00006305  SYSCFG_DL_SYSCTL_init                
000083c5  SYSCFG_DL_SYSTICK_init               
0000569d  SYSCFG_DL_UART0_init                 
00007525  SYSCFG_DL_init                       
00005281  SYSCFG_DL_initPower                  
00006415  Serial_Init                          
20200000  Serial_RxData                        
000081db  SysGetTick                           
00005a19  SysTick_Handler                      
00007769  SysTick_Increasment                  
00008439  Sys_GetTick                          
0000850d  TIMA0_IRQHandler                     
0000850d  TIMA1_IRQHandler                     
0000850d  TIMG0_IRQHandler                     
0000850d  TIMG12_IRQHandler                    
0000850d  TIMG6_IRQHandler                     
0000850d  TIMG7_IRQHandler                     
0000850d  TIMG8_IRQHandler                     
0000836f  TI_memcpy_small                      
0000841f  TI_memset_small                      
00004dd9  Task_Add                             
00006d79  Task_GraySensor                      
00006125  Task_IdleFunction                    
00003db9  Task_Init                            
00006b1d  Task_Key                             
00007201  Task_LED                             
00003fa5  Task_Motor_PID                       
0000442d  Task_OLED                            
00004e8d  Task_Serial                          
000021b9  Task_Start                           
000033dd  Task_Tracker                         
0000850d  UART0_IRQHandler                     
0000850d  UART1_IRQHandler                     
0000850d  UART2_IRQHandler                     
0000850d  UART3_IRQHandler                     
000080b5  _IQ24div                             
000080cd  _IQ24mpy                             
00007435  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009c84  __TI_CINIT_Base                      
00009c94  __TI_CINIT_Limit                     
00009c94  __TI_CINIT_Warm                      
00009c70  __TI_Handler_Table_Base              
00009c7c  __TI_Handler_Table_Limit             
00007115  __TI_auto_init_nobinit_nopinit       
00005929  __TI_decompress_lzss                 
00008381  __TI_decompress_none                 
0000646d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000081f1  __TI_zero_init_nomemset              
00002513  __adddf3                             
000047a7  __addsf3                             
00009940  __aeabi_ctype_table_                 
00009940  __aeabi_ctype_table_C                
00005a21  __aeabi_d2f                          
00006929  __aeabi_d2iz                         
00006cb5  __aeabi_d2uiz                        
00002513  __aeabi_dadd                         
00005f9d  __aeabi_dcmpeq                       
00005fd9  __aeabi_dcmpge                       
00005fed  __aeabi_dcmpgt                       
00005fc5  __aeabi_dcmple                       
00005fb1  __aeabi_dcmplt                       
00003aa5  __aeabi_ddiv                         
00004265  __aeabi_dmul                         
00002509  __aeabi_dsub                         
202004f4  __aeabi_errno                        
000084d9  __aeabi_errno_addr                   
00006df9  __aeabi_f2d                          
00007239  __aeabi_f2iz                         
000047a7  __aeabi_fadd                         
00006001  __aeabi_fcmpeq                       
0000603d  __aeabi_fcmpge                       
00006051  __aeabi_fcmpgt                       
00006029  __aeabi_fcmple                       
00006015  __aeabi_fcmplt                       
000057a5  __aeabi_fdiv                         
00005585  __aeabi_fmul                         
0000479d  __aeabi_fsub                         
0000757d  __aeabi_i2d                          
0000709d  __aeabi_i2f                          
0000651d  __aeabi_idiv                         
0000269b  __aeabi_idiv0                        
0000651d  __aeabi_idivmod                      
0000513b  __aeabi_ldiv0                        
000079c9  __aeabi_llsl                         
000078c1  __aeabi_lmul                         
000084e1  __aeabi_memcpy                       
000084e1  __aeabi_memcpy4                      
000084e1  __aeabi_memcpy8                      
000083f5  __aeabi_memset                       
000083f5  __aeabi_memset4                      
000083f5  __aeabi_memset8                      
0000789d  __aeabi_ui2d                         
00007791  __aeabi_ui2f                         
00006db9  __aeabi_uidiv                        
00006db9  __aeabi_uidivmod                     
0000830d  __aeabi_uldivmod                     
000079c9  __ashldi3                            
ffffffff  __binit__                            
00005da1  __cmpdf2                             
00007151  __cmpsf2                             
00003aa5  __divdf3                             
000057a5  __divsf3                             
00005da1  __eqdf2                              
00007151  __eqsf2                              
00006df9  __extendsfdf2                        
00006929  __fixdfsi                            
00007239  __fixsfsi                            
00006cb5  __fixunsdfsi                         
0000757d  __floatsidf                          
0000709d  __floatsisf                          
0000789d  __floatunsidf                        
00007791  __floatunsisf                        
000059a5  __gedf2                              
000070d9  __gesf2                              
000059a5  __gtdf2                              
000070d9  __gtsf2                              
00005da1  __ledf2                              
00007151  __lesf2                              
00005da1  __ltdf2                              
00007151  __ltsf2                              
UNDEFED   __mpu_init                           
00004265  __muldf3                             
000078c1  __muldi3                             
0000718d  __muldsi3                            
00005585  __mulsf3                             
00005da1  __nedf2                              
00007151  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002509  __subdf3                             
0000479d  __subsf3                             
00005a21  __truncdfsf2                         
0000513d  __udivmoddi4                         
000077b9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000852d  _system_pre_init                     
00008507  abort                                
000069bd  adc_getValue                         
00009716  asc2_0806                            
00009126  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002825  atan2                                
00002825  atan2l                               
00000df5  atanl                                
00006e39  atoi                                 
ffffffff  binit                                
00005cc9  convertAnalogToDigital               
202004f8  delayTick                            
00006a05  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006185  dmp_enable_gyro_cal                  
00006a4d  dmp_enable_lp_quat                   
00007c19  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
00008321  dmp_register_android_orient_cb       
00008335  dmp_register_tap_cb                  
00005455  dmp_set_fifo_rate                    
000029ad  dmp_set_orientation                  
00006b61  dmp_set_shake_reject_thresh          
00007341  dmp_set_shake_reject_time            
00007373  dmp_set_shake_reject_timeout         
00005e6f  dmp_set_tap_axes                     
00006ba5  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
000074c5  dmp_set_tap_time                     
000074f5  dmp_set_tap_time_multi               
20200506  enable_group1_irq                    
00006361  frexp                                
00006361  frexpl                               
00009bb8  hw                                   
00000000  interruptVectors                     
000046c5  ldexp                                
000046c5  ldexpl                               
00007969  main                                 
000078e5  memccpy                              
00007989  memcmp                               
202003d2  more                                 
000061e5  mpu6050_i2c_sda_unlock               
00004c65  mpu_configure_fifo                   
00005a95  mpu_get_accel_fsr                    
00006245  mpu_get_gyro_fsr                     
0000730d  mpu_get_sample_rate                  
00003505  mpu_init                             
00003751  mpu_load_firmware                    
00003cb9  mpu_lp_accel_mode                    
00003bb1  mpu_read_fifo_stream                 
00004f39  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00004349  mpu_set_accel_fsr                    
00002369  mpu_set_bypass                       
00004d21  mpu_set_dmp_state                    
00004add  mpu_set_gyro_fsr                     
00005321  mpu_set_int_latched                  
00004949  mpu_set_lpf                          
00004095  mpu_set_sample_rate                  
000032ad  mpu_set_sensors                      
00004fe5  mpu_write_mem                        
00002f15  mspm0_i2c_read                       
00004ba1  mspm0_i2c_write                      
00005091  normalizeAnalogValues                
00003049  qsort                                
202003a0  quat                                 
00009af7  reg                                  
000046c5  scalbn                               
000046c5  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002b25  sqrt                                 
00002b25  sqrtl                                
00009ab0  test                                 
202004fc  uwTick                               
00006e79  vsnprintf                            
000075a9  vsprintf                             
000083d5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001fe5  SYSCFG_DL_GPIO_init                  
000021b9  Task_Start                           
00002369  mpu_set_bypass                       
00002509  __aeabi_dsub                         
00002509  __subdf3                             
00002513  __adddf3                             
00002513  __aeabi_dadd                         
0000269b  __aeabi_idiv0                        
0000269d  No_MCU_Ganv_Sensor_Init              
00002825  atan2                                
00002825  atan2l                               
000029ad  dmp_set_orientation                  
00002b25  sqrt                                 
00002b25  sqrtl                                
00002c95  MPU6050_Init                         
00002f15  mspm0_i2c_read                       
00003049  qsort                                
0000317d  OLED_ShowChar                        
000032ad  mpu_set_sensors                      
000033dd  Task_Tracker                         
00003505  mpu_init                             
0000362d  PID_IQ_Prosc                         
00003751  mpu_load_firmware                    
00003995  OLED_Init                            
00003aa5  __aeabi_ddiv                         
00003aa5  __divdf3                             
00003bb1  mpu_read_fifo_stream                 
00003cb9  mpu_lp_accel_mode                    
00003db9  Task_Init                            
00003fa5  Task_Motor_PID                       
00004095  mpu_set_sample_rate                  
00004181  GROUP1_IRQHandler                    
00004265  __aeabi_dmul                         
00004265  __muldf3                             
00004349  mpu_set_accel_fsr                    
0000442d  Task_OLED                            
0000450d  DL_SYSCTL_configSYSPLL               
000045e9  Get_Analog_value                     
000046c5  ldexp                                
000046c5  ldexpl                               
000046c5  scalbn                               
000046c5  scalbnl                              
0000479d  __aeabi_fsub                         
0000479d  __subsf3                             
000047a7  __addsf3                             
000047a7  __aeabi_fadd                         
00004949  mpu_set_lpf                          
00004a19  DL_Timer_initPWMMode                 
00004add  mpu_set_gyro_fsr                     
00004ba1  mspm0_i2c_write                      
00004c65  mpu_configure_fifo                   
00004d21  mpu_set_dmp_state                    
00004dd9  Task_Add                             
00004e8d  Task_Serial                          
00004f39  mpu_read_mem                         
00004fe5  mpu_write_mem                        
00005091  normalizeAnalogValues                
0000513b  __aeabi_ldiv0                        
0000513d  __udivmoddi4                         
000051e1  Motor_SetDuty                        
00005281  SYSCFG_DL_initPower                  
00005321  mpu_set_int_latched                  
000053bd  I2C_OLED_WR_Byte                     
00005455  dmp_set_fifo_rate                    
00005585  __aeabi_fmul                         
00005585  __mulsf3                             
0000569d  SYSCFG_DL_UART0_init                 
000057a5  __aeabi_fdiv                         
000057a5  __divsf3                             
00005829  Motor_GetSpeed                       
000058a9  SYSCFG_DL_Motor_PWM_init             
00005929  __TI_decompress_lzss                 
000059a5  __gedf2                              
000059a5  __gtdf2                              
00005a19  SysTick_Handler                      
00005a21  __aeabi_d2f                          
00005a21  __truncdfsf2                         
00005a95  mpu_get_accel_fsr                    
00005b09  No_MCU_Ganv_Sensor_Init_Frist        
00005b7d  MyPrintf_DMA                         
00005bed  OLED_ShowString                      
00005c5d  Motor_Start                          
00005cc9  convertAnalogToDigital               
00005d35  I2C_OLED_Clear                       
00005da1  __cmpdf2                             
00005da1  __eqdf2                              
00005da1  __ledf2                              
00005da1  __ltdf2                              
00005da1  __nedf2                              
00005e6f  dmp_set_tap_axes                     
00005ed5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005f39  SYSCFG_DL_I2C_OLED_init              
00005f9d  __aeabi_dcmpeq                       
00005fb1  __aeabi_dcmplt                       
00005fc5  __aeabi_dcmple                       
00005fd9  __aeabi_dcmpge                       
00005fed  __aeabi_dcmpgt                       
00006001  __aeabi_fcmpeq                       
00006015  __aeabi_fcmplt                       
00006029  __aeabi_fcmple                       
0000603d  __aeabi_fcmpge                       
00006051  __aeabi_fcmpgt                       
00006065  I2C_OLED_i2c_sda_unlock              
000060c5  Key_Read                             
00006125  Task_IdleFunction                    
00006185  dmp_enable_gyro_cal                  
000061e5  mpu6050_i2c_sda_unlock               
00006245  mpu_get_gyro_fsr                     
000062a5  DL_I2C_fillControllerTXFIFO          
00006305  SYSCFG_DL_SYSCTL_init                
00006361  frexp                                
00006361  frexpl                               
000063bd  SYSCFG_DL_I2C_MPU6050_init           
00006415  Serial_Init                          
0000646d  __TI_ltoa                            
0000651d  __aeabi_idiv                         
0000651d  __aeabi_idivmod                      
000067ad  DL_DMA_initChannel                   
00006845  OLED_Printf                          
00006891  SYSCFG_DL_ADC1_init                  
00006929  __aeabi_d2iz                         
00006929  __fixdfsi                            
00006975  DL_UART_init                         
000069bd  adc_getValue                         
00006a05  dmp_enable_6x_lp_quat                
00006a4d  dmp_enable_lp_quat                   
00006a95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006ad9  PID_IQ_SetParams                     
00006b1d  Task_Key                             
00006b61  dmp_set_shake_reject_thresh          
00006ba5  dmp_set_tap_count                    
00006c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006cb5  __aeabi_d2uiz                        
00006cb5  __fixunsdfsi                         
00006cf9  DL_ADC12_setClockConfig              
00006d39  Interrupt_Init                       
00006d79  Task_GraySensor                      
00006db9  __aeabi_uidiv                        
00006db9  __aeabi_uidivmod                     
00006df9  __aeabi_f2d                          
00006df9  __extendsfdf2                        
00006e39  atoi                                 
00006e79  vsnprintf                            
00006fe9  DL_I2C_flushControllerTXFIFO         
00007025  Get_Anolog_Value                     
00007061  I2C_OLED_Set_Pos                     
0000709d  __aeabi_i2f                          
0000709d  __floatsisf                          
000070d9  __gesf2                              
000070d9  __gtsf2                              
00007115  __TI_auto_init_nobinit_nopinit       
00007151  __cmpsf2                             
00007151  __eqsf2                              
00007151  __lesf2                              
00007151  __ltsf2                              
00007151  __nesf2                              
0000718d  __muldsi3                            
000071c7  Get_Normalize_For_User               
00007201  Task_LED                             
00007239  __aeabi_f2iz                         
00007239  __fixsfsi                            
0000730d  mpu_get_sample_rate                  
00007341  dmp_set_shake_reject_time            
00007373  dmp_set_shake_reject_timeout         
00007405  SYSCFG_DL_DMA_CH_RX_init             
00007435  _IQ24toF                             
000074c5  dmp_set_tap_time                     
000074f5  dmp_set_tap_time_multi               
00007525  SYSCFG_DL_init                       
0000757d  __aeabi_i2d                          
0000757d  __floatsidf                          
000075a9  vsprintf                             
000075d5  PID_IQ_Init                          
00007769  SysTick_Increasment                  
00007791  __aeabi_ui2f                         
00007791  __floatunsisf                        
000077b9  _c_int00_noargs                      
00007853  DL_I2C_setClockConfig                
0000789d  __aeabi_ui2d                         
0000789d  __floatunsidf                        
000078c1  __aeabi_lmul                         
000078c1  __muldi3                             
000078e5  memccpy                              
00007949  Delay                                
00007969  main                                 
00007989  memcmp                               
000079c9  __aeabi_llsl                         
000079c9  __ashldi3                            
00007bc5  DL_Timer_setCaptCompUpdateMethod     
00007be1  DL_Timer_setClockConfig              
00007c19  dmp_load_motion_driver_firmware      
00007fdd  DL_Timer_setCaptureCompareOutCtl     
0000809d  SYSCFG_DL_DMA_CH_TX_init             
000080b5  _IQ24div                             
000080cd  _IQ24mpy                             
000081db  SysGetTick                           
000081f1  __TI_zero_init_nomemset              
0000830d  __aeabi_uldivmod                     
00008321  dmp_register_android_orient_cb       
00008335  dmp_register_tap_cb                  
0000835d  DL_UART_setClockConfig               
0000836f  TI_memcpy_small                      
00008381  __TI_decompress_none                 
000083b5  DL_Timer_setCaptureCompareValue      
000083c5  SYSCFG_DL_SYSTICK_init               
000083d5  wcslen                               
000083e5  Get_Digtal_For_User                  
000083f5  __aeabi_memset                       
000083f5  __aeabi_memset4                      
000083f5  __aeabi_memset8                      
0000841f  TI_memset_small                      
0000842d  SYSCFG_DL_DMA_init                   
00008439  Sys_GetTick                          
00008445  DL_Common_delayCycles                
000084d9  __aeabi_errno_addr                   
000084e1  __aeabi_memcpy                       
000084e1  __aeabi_memcpy4                      
000084e1  __aeabi_memcpy8                      
00008507  abort                                
0000850d  ADC0_IRQHandler                      
0000850d  ADC1_IRQHandler                      
0000850d  AES_IRQHandler                       
0000850d  CANFD0_IRQHandler                    
0000850d  DAC0_IRQHandler                      
0000850d  DMA_IRQHandler                       
0000850d  Default_Handler                      
0000850d  GROUP0_IRQHandler                    
0000850d  HardFault_Handler                    
0000850d  I2C0_IRQHandler                      
0000850d  I2C1_IRQHandler                      
0000850d  NMI_Handler                          
0000850d  PendSV_Handler                       
0000850d  RTC_IRQHandler                       
0000850d  SPI0_IRQHandler                      
0000850d  SPI1_IRQHandler                      
0000850d  SVC_Handler                          
0000850d  TIMA0_IRQHandler                     
0000850d  TIMA1_IRQHandler                     
0000850d  TIMG0_IRQHandler                     
0000850d  TIMG12_IRQHandler                    
0000850d  TIMG6_IRQHandler                     
0000850d  TIMG7_IRQHandler                     
0000850d  TIMG8_IRQHandler                     
0000850d  UART0_IRQHandler                     
0000850d  UART1_IRQHandler                     
0000850d  UART2_IRQHandler                     
0000850d  UART3_IRQHandler                     
00008510  C$$EXIT                              
00008511  HOSTexit                             
00008519  Reset_Handler                        
0000852d  _system_pre_init                     
00009126  asc2_1608                            
00009716  asc2_0806                            
00009940  __aeabi_ctype_table_                 
00009940  __aeabi_ctype_table_C                
00009ab0  test                                 
00009af7  reg                                  
00009bb8  hw                                   
00009c70  __TI_Handler_Table_Base              
00009c7c  __TI_Handler_Table_Limit             
00009c84  __TI_CINIT_Base                      
00009c94  __TI_CINIT_Limit                     
00009c94  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200502  Flag_MPU6050_Ready                   
20200503  Gray_Digtal                          
20200506  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[326 symbols]
