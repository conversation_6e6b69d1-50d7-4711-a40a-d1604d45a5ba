******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 15:46:40 2025

OUTPUT FILE NAME:   <TI_CAR1_8_1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000051f9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006020  00019fe0  R  X
  SRAM                  20200000   00008000  000006e1  0000791f  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006020   00006020    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005c80   00005c80    r-x .text
  00005d40    00005d40    00000280   00000280    r-- .rodata
  00005fc0    00005fc0    00000060   00000060    r-- .cinit
20200000    20200000    000004e2   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    0000010e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005c80     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000022c     MPU6050.o (.text.Read_Quad)
                  00001318    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001544    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001764    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001958    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001b34    000001d4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001d08    000001b0     Task.o (.text.Task_Start)
                  00001eb8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000204a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000204c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000021d4    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000235c    00000170            : e_sqrt.c.obj (.text.sqrt)
                  000024cc    0000013c            : _printfi.c.obj (.text.fcvt)
                  00002608    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000273c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002870    00000128     Task_App.o (.text.Task_Tracker)
                  00002998    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002ab8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002bc4    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002ccc    000000f0     Motor.o (.text.Motor_SetDirc)
                  00002dbc    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002ea0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002f84    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003060    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000313c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003214    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000032ec    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  000033b0    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003474    000000b4     Task.o (.text.Task_Add)
                  00003528    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000035d2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000035d4    000000a2                            : udivmoddi4.S.obj (.text)
                  00003676    00000002     --HOLE-- [fill = 0]
                  00003678    000000a0     Motor.o (.text.Motor_SetDuty)
                  00003718    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000037b8    00000094     Task_App.o (.text.Task_Init)
                  0000384c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000038d8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003964    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000039e8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003a6c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003aee    00000002     --HOLE-- [fill = 0]
                  00003af0    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00003b70    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003bec    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003c60    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003cd4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003d46    00000002     --HOLE-- [fill = 0]
                  00003d48    00000070     Serial.o (.text.MyPrintf_DMA)
                  00003db8    0000006c     Motor.o (.text.Motor_Start)
                  00003e24    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00003e90    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003ef8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003f5e    00000002     --HOLE-- [fill = 0]
                  00003f60    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003fc4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00004028    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000408a    00000002     --HOLE-- [fill = 0]
                  0000408c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000040ee    00000002     --HOLE-- [fill = 0]
                  000040f0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00004150    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000041b0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000420e    00000002     --HOLE-- [fill = 0]
                  00004210    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000426c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000042c8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004320    00000058     Serial.o (.text.Serial_Init)
                  00004378    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000043d0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004428    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000447e    00000002     --HOLE-- [fill = 0]
                  00004480    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  000044d4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004526    00000002     --HOLE-- [fill = 0]
                  00004528    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00004578    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000045c8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00004614    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004660    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000046ac    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000046f8    0000004c     Task_App.o (.text.Task_Serial)
                  00004744    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000478e    00000002     --HOLE-- [fill = 0]
                  00004790    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000047da    00000002     --HOLE-- [fill = 0]
                  000047dc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004824    00000048     ADC.o (.text.adc_getValue)
                  0000486c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000048b0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000048f4    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00004938    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000497a    00000002     --HOLE-- [fill = 0]
                  0000497c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000049be    00000002     --HOLE-- [fill = 0]
                  000049c0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004a00    00000040     Interrupt.o (.text.Interrupt_Init)
                  00004a40    00000040     Task_App.o (.text.Task_GraySensor)
                  00004a80    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004ac0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004b00    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004b40    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004b80    0000003e     Task.o (.text.Task_CMP)
                  00004bbe    00000002     --HOLE-- [fill = 0]
                  00004bc0    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004bfc    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004c38    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004c74    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00004cb0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004cec    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004d28    0000003c     main.o (.text.main)
                  00004d64    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004da0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004dda    00000002     --HOLE-- [fill = 0]
                  00004ddc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004e16    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00004e4e    00000002     --HOLE-- [fill = 0]
                  00004e50    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004e88    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004ebc    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004ef0    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00004f20    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004f50    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00004f80    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004fb0    00000030            : vsnprintf.c.obj (.text._outs)
                  00004fe0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000500c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00005038    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00005064    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000508e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000050b6    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000050de    00000002     --HOLE-- [fill = 0]
                  000050e0    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00005108    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00005130    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00005158    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00005180    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000051a8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000051d0    00000028     SysTick.o (.text.SysTick_Increasment)
                  000051f8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005220    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00005246    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000526c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005292    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000052b8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000052dc    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005300    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005324    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005346    00000002     --HOLE-- [fill = 0]
                  00005348    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005368    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005388    00000020     SysTick.o (.text.Delay)
                  000053a8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000053c6    00000002     --HOLE-- [fill = 0]
                  000053c8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000053e6    00000002     --HOLE-- [fill = 0]
                  000053e8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00005404    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00005420    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000543c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005458    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005474    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00005490    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000054ac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000054c8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000054e4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00005500    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  0000551c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005538    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005554    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005570    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000558c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000055a8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000055c4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000055dc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000055f4    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  0000560c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00005624    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000563c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005654    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000566c    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00005684    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000569c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000056b4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000056cc    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000056e4    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000056fc    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00005714    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000572c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005744    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000575c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005774    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000578c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000057a4    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000057bc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000057d4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000057ec    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00005804    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000581c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005834    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000584c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005864    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000587c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005894    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000058ac    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000058c4    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000058dc    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000058f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000590c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005924    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000593c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005954    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000596c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005984    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  0000599c    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000059b4    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000059cc    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  000059e2    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000059f8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005a0e    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005a24    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00005a3a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00005a50    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005a66    00000016     SysTick.o (.text.SysGetTick)
                  00005a7c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005a92    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005aa6    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005aba    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00005ace    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005ae2    00000002     --HOLE-- [fill = 0]
                  00005ae4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005af8    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005b0c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005b20    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005b34    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005b48    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005b5c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005b70    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005b84    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005b96    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005ba8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005bba    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00005bca    00000002     --HOLE-- [fill = 0]
                  00005bcc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005bdc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005bec    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005bfc    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005c0c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00005c1a    00000002     --HOLE-- [fill = 0]
                  00005c1c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005c2a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005c38    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005c46    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005c52    00000002     --HOLE-- [fill = 0]
                  00005c54    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005c60    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005c6a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005c74    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005c84    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005c8e    00000002     --HOLE-- [fill = 0]
                  00005c90    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005ca0    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005caa    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005cb4    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005cbe    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005cc8    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00005cd8    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005ce0    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005ce8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005cf0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005cf8    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005cfe    00000002     --HOLE-- [fill = 0]
                  00005d00    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005d10    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005d16    00000006            : exit.c.obj (.text:abort)
                  00005d1c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005d20    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005d24    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005d28    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005d2c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005d3c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005fc0    00000060     
                  00005fc0    0000003b     (.cinit..data.load) [load image, compression = lzss]
                  00005ffb    00000001     --HOLE-- [fill = 0]
                  00005ffc    0000000c     (__TI_handler_table)
                  00006008    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006010    00000010     (__TI_cinit_table)

.rodata    0    00005d40    00000280     
                  00005d40    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005e41    00000007     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005e48    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00005e88    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005eb0    00000028     inv_mpu.o (.rodata.test)
                  00005ed8    0000001f     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00005ef7    0000001e     inv_mpu.o (.rodata.reg)
                  00005f15    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00005f18    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005f30    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00005f48    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00005f5c    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005f6d    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005f7e    0000000c     inv_mpu.o (.rodata.hw)
                  00005f8a    0000000b     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005f95    00000001     --HOLE-- [fill = 0]
                  00005f96    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00005fa0    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005fa8    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00005fb0    00000008     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005fb8    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005fba    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005fbc    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00005fbe    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    0000010e     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004c8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004cc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004d0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004d4    00000004     SysTick.o (.data.delayTick)
                  202004d8    00000004     SysTick.o (.data.uwTick)
                  202004dc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004de    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004df    00000001     Task_App.o (.data.Gray_Digtal)
                  202004e0    00000001     Task.o (.data.Task_Num)
                  202004e1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3342    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           60      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3410    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       680     77        227    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1150    77        233    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1868    0         47     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          576     0         144    
       ADC.o                            236     0         0      
       SysTick.o                        106     0         8      
       PID_IQMath.o                     110     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5218    0         952    
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           48      0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       95        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     23628   924       1761   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006010 records: 2, size/record: 8, table size: 16
	.data: load addr=00005fc0, load size=0000003b bytes, run addr=202003d4, run size=0000010e bytes, compression=lzss
	.bss: load addr=00006008, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005ffc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001eb9     00005c74     00005c72   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002ea1     00005c90     00005c8c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005ca8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005cbc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005cde          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00005d14          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002ab9     00005cc8     00005cc6   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001ec3     00005d00     00005cfc   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005d26          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000051f9     00005d2c     00005d28   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005d1d  ADC0_IRQHandler                      
00005d1d  ADC1_IRQHandler                      
00005d1d  AES_IRQHandler                       
00005d20  C$$EXIT                              
00005d1d  CANFD0_IRQHandler                    
00005d1d  DAC0_IRQHandler                      
000049c1  DL_ADC12_setClockConfig              
00005c61  DL_Common_delayCycles                
00004615  DL_DMA_initChannel                   
000041b1  DL_I2C_fillControllerTXFIFO          
00004c39  DL_I2C_flushControllerTXFIFO         
00005293  DL_I2C_setClockConfig                
00002f85  DL_SYSCTL_configSYSPLL               
00003f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000486d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000032ed  DL_Timer_initPWMMode                 
00005571  DL_Timer_setCaptCompUpdateMethod     
000058ad  DL_Timer_setCaptureCompareOutCtl     
00005bdd  DL_Timer_setCaptureCompareValue      
0000558d  DL_Timer_setClockConfig              
000047dd  DL_UART_init                         
00005b85  DL_UART_setClockConfig               
00005d1d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004c8  Data_MotorEncoder                    
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004c0  Data_Tracker_Input                   
202004cc  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00005d1d  Default_Handler                      
00005389  Delay                                
202003c8  ExISR_Flag                           
202004de  Flag_MPU6050_Ready                   
00005d1d  GROUP0_IRQHandler                    
00002dbd  GROUP1_IRQHandler                    
00003061  Get_Analog_value                     
00004c75  Get_Anolog_Value                     
00005c0d  Get_Digtal_For_User                  
00004e17  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
202004df  Gray_Digtal                          
202004a0  Gray_Normal                          
00005d21  HOSTexit                             
00005d1d  HardFault_Handler                    
00005d1d  I2C0_IRQHandler                      
00005d1d  I2C1_IRQHandler                      
00004a01  Interrupt_Init                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
00003679  Motor_SetDuty                        
00003db9  Motor_Start                          
00003d49  MyPrintf_DMA                         
00005d1d  NMI_Handler                          
0000204d  No_MCU_Ganv_Sensor_Init              
00003cd5  No_MCU_Ganv_Sensor_Init_Frist        
00004939  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005065  PID_IQ_Init                          
000048b1  PID_IQ_SetParams                     
00005d1d  PendSV_Handler                       
00005d1d  RTC_IRQHandler                       
000010ed  Read_Quad                            
00005d29  Reset_Handler                        
00005d1d  SPI0_IRQHandler                      
00005d1d  SPI1_IRQHandler                      
00005d1d  SVC_Handler                          
000046ad  SYSCFG_DL_ADC1_init                  
00004f51  SYSCFG_DL_DMA_CH_RX_init             
0000596d  SYSCFG_DL_DMA_CH_TX_init             
00005c47  SYSCFG_DL_DMA_init                   
00001b35  SYSCFG_DL_GPIO_init                  
000042c9  SYSCFG_DL_I2C_MPU6050_init           
00003fc5  SYSCFG_DL_I2C_OLED_init              
00003af1  SYSCFG_DL_Motor_PWM_init             
00004211  SYSCFG_DL_SYSCTL_init                
00005bed  SYSCFG_DL_SYSTICK_init               
00003965  SYSCFG_DL_UART0_init                 
00004fe1  SYSCFG_DL_init                       
00003719  SYSCFG_DL_initPower                  
00004321  Serial_Init                          
20200000  Serial_RxData                        
00005a67  SysGetTick                           
00005ce1  SysTick_Handler                      
000051d1  SysTick_Increasment                  
00005c55  Sys_GetTick                          
00005d1d  TIMA0_IRQHandler                     
00005d1d  TIMA1_IRQHandler                     
00005d1d  TIMG0_IRQHandler                     
00005d1d  TIMG12_IRQHandler                    
00005d1d  TIMG6_IRQHandler                     
00005d1d  TIMG7_IRQHandler                     
00005d1d  TIMG8_IRQHandler                     
00005b97  TI_memcpy_small                      
00005c39  TI_memset_small                      
00003475  Task_Add                             
00004a41  Task_GraySensor                      
000040f1  Task_IdleFunction                    
000037b9  Task_Init                            
000046f9  Task_Serial                          
00001d09  Task_Start                           
00002871  Task_Tracker                         
00005d1d  UART0_IRQHandler                     
00005d1d  UART1_IRQHandler                     
00005d1d  UART2_IRQHandler                     
00005d1d  UART3_IRQHandler                     
00005985  _IQ24div                             
0000599d  _IQ24mpy                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006010  __TI_CINIT_Base                      
00006020  __TI_CINIT_Limit                     
00006020  __TI_CINIT_Warm                      
00005ffc  __TI_Handler_Table_Base              
00006008  __TI_Handler_Table_Limit             
00004d65  __TI_auto_init_nobinit_nopinit       
00003b71  __TI_decompress_lzss                 
00005ba9  __TI_decompress_none                 
00004379  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005a7d  __TI_zero_init_nomemset              
00001ec3  __adddf3                             
0000321f  __addsf3                             
00005d40  __aeabi_ctype_table_                 
00005d40  __aeabi_ctype_table_C                
00003c61  __aeabi_d2f                          
00004791  __aeabi_d2iz                         
0000497d  __aeabi_d2uiz                        
00001ec3  __aeabi_dadd                         
00004029  __aeabi_dcmpeq                       
00004065  __aeabi_dcmpge                       
00004079  __aeabi_dcmpgt                       
00004051  __aeabi_dcmple                       
0000403d  __aeabi_dcmplt                       
00002ab9  __aeabi_ddiv                         
00002ea1  __aeabi_dmul                         
00001eb9  __aeabi_dsub                         
202004d0  __aeabi_errno                        
00005ce9  __aeabi_errno_addr                   
00004ac1  __aeabi_f2d                          
00004e51  __aeabi_f2iz                         
0000321f  __aeabi_fadd                         
0000408d  __aeabi_fcmpeq                       
000040c9  __aeabi_fcmpge                       
000040dd  __aeabi_fcmpgt                       
000040b5  __aeabi_fcmple                       
000040a1  __aeabi_fcmplt                       
00003a6d  __aeabi_fdiv                         
0000384d  __aeabi_fmul                         
00003215  __aeabi_fsub                         
00005039  __aeabi_i2d                          
00004cb1  __aeabi_i2f                          
00004429  __aeabi_idiv                         
0000204b  __aeabi_idiv0                        
00004429  __aeabi_idivmod                      
000035d3  __aeabi_ldiv0                        
000053c9  __aeabi_llsl                         
00005301  __aeabi_lmul                         
00005cf1  __aeabi_memcpy                       
00005cf1  __aeabi_memcpy4                      
00005cf1  __aeabi_memcpy8                      
00005c1d  __aeabi_memset                       
00005c1d  __aeabi_memset4                      
00005c1d  __aeabi_memset8                      
000052dd  __aeabi_ui2d                         
00004a81  __aeabi_uidiv                        
00004a81  __aeabi_uidivmod                     
00005b5d  __aeabi_uldivmod                     
000053c9  __ashldi3                            
ffffffff  __binit__                            
00003e91  __cmpdf2                             
00004da1  __cmpsf2                             
00002ab9  __divdf3                             
00003a6d  __divsf3                             
00003e91  __eqdf2                              
00004da1  __eqsf2                              
00004ac1  __extendsfdf2                        
00004791  __fixdfsi                            
00004e51  __fixsfsi                            
0000497d  __fixunsdfsi                         
00005039  __floatsidf                          
00004cb1  __floatsisf                          
000052dd  __floatunsidf                        
00003bed  __gedf2                              
00004ced  __gesf2                              
00003bed  __gtdf2                              
00004ced  __gtsf2                              
00003e91  __ledf2                              
00004da1  __lesf2                              
00003e91  __ltdf2                              
00004da1  __ltsf2                              
UNDEFED   __mpu_init                           
00002ea1  __muldf3                             
00005301  __muldi3                             
00004ddd  __muldsi3                            
0000384d  __mulsf3                             
00003e91  __nedf2                              
00004da1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001eb9  __subdf3                             
00003215  __subsf3                             
00003c61  __truncdfsf2                         
000035d5  __udivmoddi4                         
000051f9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00005d3d  _system_pre_init                     
00005d17  abort                                
00004825  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000021d5  atan2                                
000021d5  atan2l                               
00000df5  atanl                                
00004b01  atoi                                 
ffffffff  binit                                
00003e25  convertAnalogToDigital               
202004d4  delayTick                            
00001765  dmp_read_fifo                        
202004e1  enable_group1_irq                    
0000426d  frexp                                
0000426d  frexpl                               
00005f7e  hw                                   
00000000  interruptVectors                     
0000313d  ldexp                                
0000313d  ldexpl                               
00004d29  main                                 
00005325  memccpy                              
202003d2  more                                 
00004151  mpu6050_i2c_sda_unlock               
00002bc5  mpu_read_fifo_stream                 
00001319  mpu_reset_fifo                       
00002609  mspm0_i2c_read                       
000033b1  mspm0_i2c_write                      
00003529  normalizeAnalogValues                
0000273d  qsort                                
202003a0  quat                                 
00005ef7  reg                                  
0000313d  scalbn                               
0000313d  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
0000235d  sqrt                                 
0000235d  sqrtl                                
00005eb0  test                                 
202004d8  uwTick                               
00004b41  vsnprintf                            
00005bfd  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Read_Quad                            
00001319  mpu_reset_fifo                       
00001765  dmp_read_fifo                        
00001b35  SYSCFG_DL_GPIO_init                  
00001d09  Task_Start                           
00001eb9  __aeabi_dsub                         
00001eb9  __subdf3                             
00001ec3  __adddf3                             
00001ec3  __aeabi_dadd                         
0000204b  __aeabi_idiv0                        
0000204d  No_MCU_Ganv_Sensor_Init              
000021d5  atan2                                
000021d5  atan2l                               
0000235d  sqrt                                 
0000235d  sqrtl                                
00002609  mspm0_i2c_read                       
0000273d  qsort                                
00002871  Task_Tracker                         
00002ab9  __aeabi_ddiv                         
00002ab9  __divdf3                             
00002bc5  mpu_read_fifo_stream                 
00002dbd  GROUP1_IRQHandler                    
00002ea1  __aeabi_dmul                         
00002ea1  __muldf3                             
00002f85  DL_SYSCTL_configSYSPLL               
00003061  Get_Analog_value                     
0000313d  ldexp                                
0000313d  ldexpl                               
0000313d  scalbn                               
0000313d  scalbnl                              
00003215  __aeabi_fsub                         
00003215  __subsf3                             
0000321f  __addsf3                             
0000321f  __aeabi_fadd                         
000032ed  DL_Timer_initPWMMode                 
000033b1  mspm0_i2c_write                      
00003475  Task_Add                             
00003529  normalizeAnalogValues                
000035d3  __aeabi_ldiv0                        
000035d5  __udivmoddi4                         
00003679  Motor_SetDuty                        
00003719  SYSCFG_DL_initPower                  
000037b9  Task_Init                            
0000384d  __aeabi_fmul                         
0000384d  __mulsf3                             
00003965  SYSCFG_DL_UART0_init                 
00003a6d  __aeabi_fdiv                         
00003a6d  __divsf3                             
00003af1  SYSCFG_DL_Motor_PWM_init             
00003b71  __TI_decompress_lzss                 
00003bed  __gedf2                              
00003bed  __gtdf2                              
00003c61  __aeabi_d2f                          
00003c61  __truncdfsf2                         
00003cd5  No_MCU_Ganv_Sensor_Init_Frist        
00003d49  MyPrintf_DMA                         
00003db9  Motor_Start                          
00003e25  convertAnalogToDigital               
00003e91  __cmpdf2                             
00003e91  __eqdf2                              
00003e91  __ledf2                              
00003e91  __ltdf2                              
00003e91  __nedf2                              
00003f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003fc5  SYSCFG_DL_I2C_OLED_init              
00004029  __aeabi_dcmpeq                       
0000403d  __aeabi_dcmplt                       
00004051  __aeabi_dcmple                       
00004065  __aeabi_dcmpge                       
00004079  __aeabi_dcmpgt                       
0000408d  __aeabi_fcmpeq                       
000040a1  __aeabi_fcmplt                       
000040b5  __aeabi_fcmple                       
000040c9  __aeabi_fcmpge                       
000040dd  __aeabi_fcmpgt                       
000040f1  Task_IdleFunction                    
00004151  mpu6050_i2c_sda_unlock               
000041b1  DL_I2C_fillControllerTXFIFO          
00004211  SYSCFG_DL_SYSCTL_init                
0000426d  frexp                                
0000426d  frexpl                               
000042c9  SYSCFG_DL_I2C_MPU6050_init           
00004321  Serial_Init                          
00004379  __TI_ltoa                            
00004429  __aeabi_idiv                         
00004429  __aeabi_idivmod                      
00004615  DL_DMA_initChannel                   
000046ad  SYSCFG_DL_ADC1_init                  
000046f9  Task_Serial                          
00004791  __aeabi_d2iz                         
00004791  __fixdfsi                            
000047dd  DL_UART_init                         
00004825  adc_getValue                         
0000486d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000048b1  PID_IQ_SetParams                     
00004939  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000497d  __aeabi_d2uiz                        
0000497d  __fixunsdfsi                         
000049c1  DL_ADC12_setClockConfig              
00004a01  Interrupt_Init                       
00004a41  Task_GraySensor                      
00004a81  __aeabi_uidiv                        
00004a81  __aeabi_uidivmod                     
00004ac1  __aeabi_f2d                          
00004ac1  __extendsfdf2                        
00004b01  atoi                                 
00004b41  vsnprintf                            
00004c39  DL_I2C_flushControllerTXFIFO         
00004c75  Get_Anolog_Value                     
00004cb1  __aeabi_i2f                          
00004cb1  __floatsisf                          
00004ced  __gesf2                              
00004ced  __gtsf2                              
00004d29  main                                 
00004d65  __TI_auto_init_nobinit_nopinit       
00004da1  __cmpsf2                             
00004da1  __eqsf2                              
00004da1  __lesf2                              
00004da1  __ltsf2                              
00004da1  __nesf2                              
00004ddd  __muldsi3                            
00004e17  Get_Normalize_For_User               
00004e51  __aeabi_f2iz                         
00004e51  __fixsfsi                            
00004f51  SYSCFG_DL_DMA_CH_RX_init             
00004fe1  SYSCFG_DL_init                       
00005039  __aeabi_i2d                          
00005039  __floatsidf                          
00005065  PID_IQ_Init                          
000051d1  SysTick_Increasment                  
000051f9  _c_int00_noargs                      
00005293  DL_I2C_setClockConfig                
000052dd  __aeabi_ui2d                         
000052dd  __floatunsidf                        
00005301  __aeabi_lmul                         
00005301  __muldi3                             
00005325  memccpy                              
00005389  Delay                                
000053c9  __aeabi_llsl                         
000053c9  __ashldi3                            
00005571  DL_Timer_setCaptCompUpdateMethod     
0000558d  DL_Timer_setClockConfig              
000058ad  DL_Timer_setCaptureCompareOutCtl     
0000596d  SYSCFG_DL_DMA_CH_TX_init             
00005985  _IQ24div                             
0000599d  _IQ24mpy                             
00005a67  SysGetTick                           
00005a7d  __TI_zero_init_nomemset              
00005b5d  __aeabi_uldivmod                     
00005b85  DL_UART_setClockConfig               
00005b97  TI_memcpy_small                      
00005ba9  __TI_decompress_none                 
00005bdd  DL_Timer_setCaptureCompareValue      
00005bed  SYSCFG_DL_SYSTICK_init               
00005bfd  wcslen                               
00005c0d  Get_Digtal_For_User                  
00005c1d  __aeabi_memset                       
00005c1d  __aeabi_memset4                      
00005c1d  __aeabi_memset8                      
00005c39  TI_memset_small                      
00005c47  SYSCFG_DL_DMA_init                   
00005c55  Sys_GetTick                          
00005c61  DL_Common_delayCycles                
00005ce1  SysTick_Handler                      
00005ce9  __aeabi_errno_addr                   
00005cf1  __aeabi_memcpy                       
00005cf1  __aeabi_memcpy4                      
00005cf1  __aeabi_memcpy8                      
00005d17  abort                                
00005d1d  ADC0_IRQHandler                      
00005d1d  ADC1_IRQHandler                      
00005d1d  AES_IRQHandler                       
00005d1d  CANFD0_IRQHandler                    
00005d1d  DAC0_IRQHandler                      
00005d1d  DMA_IRQHandler                       
00005d1d  Default_Handler                      
00005d1d  GROUP0_IRQHandler                    
00005d1d  HardFault_Handler                    
00005d1d  I2C0_IRQHandler                      
00005d1d  I2C1_IRQHandler                      
00005d1d  NMI_Handler                          
00005d1d  PendSV_Handler                       
00005d1d  RTC_IRQHandler                       
00005d1d  SPI0_IRQHandler                      
00005d1d  SPI1_IRQHandler                      
00005d1d  SVC_Handler                          
00005d1d  TIMA0_IRQHandler                     
00005d1d  TIMA1_IRQHandler                     
00005d1d  TIMG0_IRQHandler                     
00005d1d  TIMG12_IRQHandler                    
00005d1d  TIMG6_IRQHandler                     
00005d1d  TIMG7_IRQHandler                     
00005d1d  TIMG8_IRQHandler                     
00005d1d  UART0_IRQHandler                     
00005d1d  UART1_IRQHandler                     
00005d1d  UART2_IRQHandler                     
00005d1d  UART3_IRQHandler                     
00005d20  C$$EXIT                              
00005d21  HOSTexit                             
00005d29  Reset_Handler                        
00005d3d  _system_pre_init                     
00005d40  __aeabi_ctype_table_                 
00005d40  __aeabi_ctype_table_C                
00005eb0  test                                 
00005ef7  reg                                  
00005f7e  hw                                   
00005ffc  __TI_Handler_Table_Base              
00006008  __TI_Handler_Table_Limit             
00006010  __TI_CINIT_Base                      
00006020  __TI_CINIT_Limit                     
00006020  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004c0  Data_Tracker_Input                   
202004c8  Data_MotorEncoder                    
202004cc  Data_Tracker_Offset                  
202004d0  __aeabi_errno                        
202004d4  delayTick                            
202004d8  uwTick                               
202004de  Flag_MPU6050_Ready                   
202004df  Gray_Digtal                          
202004e1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[266 symbols]
