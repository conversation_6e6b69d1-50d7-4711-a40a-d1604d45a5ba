******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 17:09:00 2025

OUTPUT FILE NAME:   <TI_CAR1_8_1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005569


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000063c0  00019c40  R  X
  SRAM                  20200000   00008000  000006ef  00007911  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000063c0   000063c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005ff0   00005ff0    r-x .text
  000060b0    000060b0    000002a0   000002a0    r-- .rodata
  00006350    00006350    00000070   00000070    r-- .cinit
20200000    20200000    000004f0   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    0000011c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005ff0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000022c     MPU6050.o (.text.Read_Quad)
                  00001318    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001544    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001764    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001958    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001b34    000001d4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001d08    000001b0     Task.o (.text.Task_Start)
                  00001eb8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000204a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000204c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000021d4    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000235c    00000170            : e_sqrt.c.obj (.text.sqrt)
                  000024cc    0000013c            : _printfi.c.obj (.text.fcvt)
                  00002608    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000273c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002870    00000128     Task_App.o (.text.Task_Tracker)
                  00002998    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00002abc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002bdc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002ce8    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002df0    000000f0     Motor.o (.text.Motor_SetDirc)
                  00002ee0    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00002fd0    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000030b4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003198    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003274    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00003350    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003428    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003500    000000d0     Task_App.o (.text.Task_Init)
                  000035d0    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00003694    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003758    000000b4     Task.o (.text.Task_Add)
                  0000380c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000038b6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000038b8    000000a2                            : udivmoddi4.S.obj (.text)
                  0000395a    00000002     --HOLE-- [fill = 0]
                  0000395c    000000a0     Motor.o (.text.Motor_SetDuty)
                  000039fc    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a9c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003b28    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003bb4    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003c38    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003cbc    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003d3e    00000002     --HOLE-- [fill = 0]
                  00003d40    00000080     Motor.o (.text.Motor_GetSpeed)
                  00003dc0    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00003e40    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003ebc    00000078     Task_App.o (.text.Task_Serial)
                  00003f34    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003fa8    00000008     Interrupt.o (.text.SysTick_Handler)
                  00003fb0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004024    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004096    00000002     --HOLE-- [fill = 0]
                  00004098    00000070     Serial.o (.text.MyPrintf_DMA)
                  00004108    0000006c     Motor.o (.text.Motor_Start)
                  00004174    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000041e0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00004248    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000042ae    00000002     --HOLE-- [fill = 0]
                  000042b0    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00004314    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00004378    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000043da    00000002     --HOLE-- [fill = 0]
                  000043dc    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000443e    00000002     --HOLE-- [fill = 0]
                  00004440    00000060     Task_App.o (.text.Task_GraySensor)
                  000044a0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00004500    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00004560    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000045be    00000002     --HOLE-- [fill = 0]
                  000045c0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000461c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004678    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000046d0    00000058     Serial.o (.text.Serial_Init)
                  00004728    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004780    00000058            : _printfi.c.obj (.text._pconv_f)
                  000047d8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000482e    00000002     --HOLE-- [fill = 0]
                  00004830    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  00004884    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000048d6    00000002     --HOLE-- [fill = 0]
                  000048d8    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00004928    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00004978    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000049c4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004a10    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004a5c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004aa8    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004af2    00000002     --HOLE-- [fill = 0]
                  00004af4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004b3e    00000002     --HOLE-- [fill = 0]
                  00004b40    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004b88    00000048     ADC.o (.text.adc_getValue)
                  00004bd0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004c14    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00004c58    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00004c9c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00004cde    00000002     --HOLE-- [fill = 0]
                  00004ce0    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004d22    00000002     --HOLE-- [fill = 0]
                  00004d24    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004d64    00000040     Interrupt.o (.text.Interrupt_Init)
                  00004da4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004de4    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004e24    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004e64    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004ea4    0000003e     Task.o (.text.Task_CMP)
                  00004ee2    00000002     --HOLE-- [fill = 0]
                  00004ee4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004f20    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004f5c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004f98    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00004fd4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00005010    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000504c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00005088    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000050c2    00000002     --HOLE-- [fill = 0]
                  000050c4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000050fe    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00005136    00000002     --HOLE-- [fill = 0]
                  00005138    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005170    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000051a4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000051d8    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00005208    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00005238    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00005268    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00005298    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000052c8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000052f8    00000030            : vsnprintf.c.obj (.text._outs)
                  00005328    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005354    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00005380    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000053ac    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000053d6    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000053fe    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005426    00000002     --HOLE-- [fill = 0]
                  00005428    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00005450    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00005478    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000054a0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000054c8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000054f0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005518    00000028     SysTick.o (.text.SysTick_Increasment)
                  00005540    00000028     main.o (.text.main)
                  00005568    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005590    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000055b6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000055dc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005602    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005628    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000564c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005670    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005694    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000056b6    00000002     --HOLE-- [fill = 0]
                  000056b8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000056d8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000056f8    00000020     SysTick.o (.text.Delay)
                  00005718    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005736    00000002     --HOLE-- [fill = 0]
                  00005738    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005756    00000002     --HOLE-- [fill = 0]
                  00005758    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00005774    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00005790    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000057ac    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000057c8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000057e4    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00005800    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000581c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005838    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005854    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00005870    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  0000588c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000058a8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000058c4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000058e0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000058fc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005918    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005934    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  0000594c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005964    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  0000597c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00005994    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000059ac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000059c4    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000059dc    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000059f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005a0c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005a24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005a3c    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00005a54    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00005a6c    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00005a84    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005a9c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005ab4    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00005acc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005ae4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005afc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005b14    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00005b2c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005b44    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00005b5c    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00005b74    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005b8c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005ba4    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005bbc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005bd4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005bec    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005c04    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005c1c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005c34    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00005c4c    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00005c64    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005c7c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005c94    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005cac    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005cc4    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005cdc    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005cf4    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00005d0c    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00005d24    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00005d3c    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00005d52    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00005d68    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005d7e    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005d94    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00005daa    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00005dc0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005dd6    00000016     SysTick.o (.text.SysGetTick)
                  00005dec    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005e02    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005e16    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005e2a    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00005e3e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005e52    00000002     --HOLE-- [fill = 0]
                  00005e54    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005e68    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005e7c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005e90    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005ea4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005eb8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005ecc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005ee0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005ef4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005f06    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005f18    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005f2a    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00005f3a    00000002     --HOLE-- [fill = 0]
                  00005f3c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005f4c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005f5c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005f6c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005f7c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00005f8a    00000002     --HOLE-- [fill = 0]
                  00005f8c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005f9a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005fa8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005fb6    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005fc2    00000002     --HOLE-- [fill = 0]
                  00005fc4    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005fd0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005fda    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005fe4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005ff4    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005ffe    00000002     --HOLE-- [fill = 0]
                  00006000    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00006010    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000601a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006024    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000602e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00006038    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00006048    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006050    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00006058    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006060    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006066    00000002     --HOLE-- [fill = 0]
                  00006068    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00006078    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000607e    00000006            : exit.c.obj (.text:abort)
                  00006084    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00006088    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000608c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00006090    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00006094    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000060a4    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000060a8    00000008     --HOLE-- [fill = 0]

.cinit     0    00006350    00000070     
                  00006350    00000048     (.cinit..data.load) [load image, compression = lzss]
                  00006398    0000000c     (__TI_handler_table)
                  000063a4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000063ac    00000010     (__TI_cinit_table)
                  000063bc    00000004     --HOLE-- [fill = 0]

.rodata    0    000060b0    000002a0     
                  000060b0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000061b1    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000061b8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000061f8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006220    00000028     inv_mpu.o (.rodata.test)
                  00006248    0000001f     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00006267    0000001e     inv_mpu.o (.rodata.reg)
                  00006285    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00006288    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000062a0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000062b8    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  000062cc    00000014     Task_App.o (.rodata.str1.3850258909703972507.1)
                  000062e0    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000062f1    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00006302    0000000c     inv_mpu.o (.rodata.hw)
                  0000630e    0000000b     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00006319    00000001     --HOLE-- [fill = 0]
                  0000631a    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00006324    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000632c    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00006334    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000633c    00000006     main.o (.rodata.str1.15159059442110792349.1)
                  00006342    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006348    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  0000634a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000634c    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000634e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    0000011c     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004c8    00000008     Task_App.o (.data.Motor)
                  202004d0    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004d4    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004d8    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004dc    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004e0    00000004     SysTick.o (.data.delayTick)
                  202004e4    00000004     SysTick.o (.data.uwTick)
                  202004e8    00000002     Task_App.o (.data.Task_GraySensor.debug_cnt)
                  202004ea    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ec    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ed    00000001     Task_App.o (.data.Gray_Digtal)
                  202004ee    00000001     Task.o (.data.Task_Num)
                  202004ef    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3390    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           40      6         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3438    321       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1056    103       241    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1526    103       247    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1868    0         47     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5638    0         952    
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       108       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     24500   969       1775   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000063ac records: 2, size/record: 8, table size: 16
	.data: load addr=00006350, load size=00000048 bytes, run addr=202003d4, run size=0000011c bytes, compression=lzss
	.bss: load addr=000063a4, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006398 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001eb9     00005fe4     00005fe2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000030b5     00006000     00005ffc   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006018          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000602c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000604e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             0000607c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002bdd     00006038     00006036   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001ec3     00006068     00006064   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000608e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005569     00006094     00006090   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00006085  ADC0_IRQHandler                      
00006085  ADC1_IRQHandler                      
00006085  AES_IRQHandler                       
00006088  C$$EXIT                              
00006085  CANFD0_IRQHandler                    
00006085  DAC0_IRQHandler                      
00004d25  DL_ADC12_setClockConfig              
00005fd1  DL_Common_delayCycles                
000049c5  DL_DMA_initChannel                   
00004561  DL_I2C_fillControllerTXFIFO          
00004f5d  DL_I2C_flushControllerTXFIFO         
00005603  DL_I2C_setClockConfig                
00003199  DL_SYSCTL_configSYSPLL               
000042b1  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004bd1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000035d1  DL_Timer_initPWMMode                 
000058e1  DL_Timer_setCaptCompUpdateMethod     
00005c1d  DL_Timer_setCaptureCompareOutCtl     
00005f4d  DL_Timer_setCaptureCompareValue      
000058fd  DL_Timer_setClockConfig              
00004b41  DL_UART_init                         
00005ef5  DL_UART_setClockConfig               
00006085  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004d0  Data_MotorEncoder                    
202004d4  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004c0  Data_Tracker_Input                   
202004d8  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00006085  Default_Handler                      
000056f9  Delay                                
202003c8  ExISR_Flag                           
202004ec  Flag_MPU6050_Ready                   
00006085  GROUP0_IRQHandler                    
00002fd1  GROUP1_IRQHandler                    
00003275  Get_Analog_value                     
00004f99  Get_Anolog_Value                     
00005f7d  Get_Digtal_For_User                  
000050ff  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
202004ed  Gray_Digtal                          
202004a0  Gray_Normal                          
00006089  HOSTexit                             
00006085  HardFault_Handler                    
00006085  I2C0_IRQHandler                      
00006085  I2C1_IRQHandler                      
00004d65  Interrupt_Init                       
202004c8  Motor                                
00003d41  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
0000395d  Motor_SetDuty                        
00004109  Motor_Start                          
00004099  MyPrintf_DMA                         
00006085  NMI_Handler                          
0000204d  No_MCU_Ganv_Sensor_Init              
00004025  No_MCU_Ganv_Sensor_Init_Frist        
00004c9d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000053ad  PID_IQ_Init                          
00002999  PID_IQ_Prosc                         
00004c15  PID_IQ_SetParams                     
00006085  PendSV_Handler                       
00006085  RTC_IRQHandler                       
000010ed  Read_Quad                            
00006091  Reset_Handler                        
00006085  SPI0_IRQHandler                      
00006085  SPI1_IRQHandler                      
00006085  SVC_Handler                          
00004a5d  SYSCFG_DL_ADC1_init                  
00005269  SYSCFG_DL_DMA_CH_RX_init             
00005cdd  SYSCFG_DL_DMA_CH_TX_init             
00005fb7  SYSCFG_DL_DMA_init                   
00001b35  SYSCFG_DL_GPIO_init                  
00004679  SYSCFG_DL_I2C_MPU6050_init           
00004315  SYSCFG_DL_I2C_OLED_init              
00003dc1  SYSCFG_DL_Motor_PWM_init             
000045c1  SYSCFG_DL_SYSCTL_init                
00005f5d  SYSCFG_DL_SYSTICK_init               
00003bb5  SYSCFG_DL_UART0_init                 
00005329  SYSCFG_DL_init                       
000039fd  SYSCFG_DL_initPower                  
000046d1  Serial_Init                          
20200000  Serial_RxData                        
00005dd7  SysGetTick                           
00003fa9  SysTick_Handler                      
00005519  SysTick_Increasment                  
00005fc5  Sys_GetTick                          
00006085  TIMA0_IRQHandler                     
00006085  TIMA1_IRQHandler                     
00006085  TIMG0_IRQHandler                     
00006085  TIMG12_IRQHandler                    
00006085  TIMG6_IRQHandler                     
00006085  TIMG7_IRQHandler                     
00006085  TIMG8_IRQHandler                     
00005f07  TI_memcpy_small                      
00005fa9  TI_memset_small                      
00003759  Task_Add                             
00004441  Task_GraySensor                      
000044a1  Task_IdleFunction                    
00003501  Task_Init                            
00002ee1  Task_Motor_PID                       
00003ebd  Task_Serial                          
00001d09  Task_Start                           
00002871  Task_Tracker                         
00006085  UART0_IRQHandler                     
00006085  UART1_IRQHandler                     
00006085  UART2_IRQHandler                     
00006085  UART3_IRQHandler                     
00005cf5  _IQ24div                             
00005d0d  _IQ24mpy                             
00005299  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000063ac  __TI_CINIT_Base                      
000063bc  __TI_CINIT_Limit                     
000063bc  __TI_CINIT_Warm                      
00006398  __TI_Handler_Table_Base              
000063a4  __TI_Handler_Table_Limit             
0000504d  __TI_auto_init_nobinit_nopinit       
00003e41  __TI_decompress_lzss                 
00005f19  __TI_decompress_none                 
00004729  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005ded  __TI_zero_init_nomemset              
00001ec3  __adddf3                             
00003433  __addsf3                             
000060b0  __aeabi_ctype_table_                 
000060b0  __aeabi_ctype_table_C                
00003fb1  __aeabi_d2f                          
00004af5  __aeabi_d2iz                         
00004ce1  __aeabi_d2uiz                        
00001ec3  __aeabi_dadd                         
00004379  __aeabi_dcmpeq                       
000043b5  __aeabi_dcmpge                       
000043c9  __aeabi_dcmpgt                       
000043a1  __aeabi_dcmple                       
0000438d  __aeabi_dcmplt                       
00002bdd  __aeabi_ddiv                         
000030b5  __aeabi_dmul                         
00001eb9  __aeabi_dsub                         
202004dc  __aeabi_errno                        
00006051  __aeabi_errno_addr                   
00004de5  __aeabi_f2d                          
00005139  __aeabi_f2iz                         
00003433  __aeabi_fadd                         
000043dd  __aeabi_fcmpeq                       
00004419  __aeabi_fcmpge                       
0000442d  __aeabi_fcmpgt                       
00004405  __aeabi_fcmple                       
000043f1  __aeabi_fcmplt                       
00003cbd  __aeabi_fdiv                         
00003a9d  __aeabi_fmul                         
00003429  __aeabi_fsub                         
00005381  __aeabi_i2d                          
00004fd5  __aeabi_i2f                          
000047d9  __aeabi_idiv                         
0000204b  __aeabi_idiv0                        
000047d9  __aeabi_idivmod                      
000038b7  __aeabi_ldiv0                        
00005739  __aeabi_llsl                         
00005671  __aeabi_lmul                         
00006059  __aeabi_memcpy                       
00006059  __aeabi_memcpy4                      
00006059  __aeabi_memcpy8                      
00005f8d  __aeabi_memset                       
00005f8d  __aeabi_memset4                      
00005f8d  __aeabi_memset8                      
0000564d  __aeabi_ui2d                         
00004da5  __aeabi_uidiv                        
00004da5  __aeabi_uidivmod                     
00005ecd  __aeabi_uldivmod                     
00005739  __ashldi3                            
ffffffff  __binit__                            
000041e1  __cmpdf2                             
00005089  __cmpsf2                             
00002bdd  __divdf3                             
00003cbd  __divsf3                             
000041e1  __eqdf2                              
00005089  __eqsf2                              
00004de5  __extendsfdf2                        
00004af5  __fixdfsi                            
00005139  __fixsfsi                            
00004ce1  __fixunsdfsi                         
00005381  __floatsidf                          
00004fd5  __floatsisf                          
0000564d  __floatunsidf                        
00003f35  __gedf2                              
00005011  __gesf2                              
00003f35  __gtdf2                              
00005011  __gtsf2                              
000041e1  __ledf2                              
00005089  __lesf2                              
000041e1  __ltdf2                              
00005089  __ltsf2                              
UNDEFED   __mpu_init                           
000030b5  __muldf3                             
00005671  __muldi3                             
000050c5  __muldsi3                            
00003a9d  __mulsf3                             
000041e1  __nedf2                              
00005089  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001eb9  __subdf3                             
00003429  __subsf3                             
00003fb1  __truncdfsf2                         
000038b9  __udivmoddi4                         
00005569  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000060a5  _system_pre_init                     
0000607f  abort                                
00004b89  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000021d5  atan2                                
000021d5  atan2l                               
00000df5  atanl                                
00004e25  atoi                                 
ffffffff  binit                                
00004175  convertAnalogToDigital               
202004e0  delayTick                            
00001765  dmp_read_fifo                        
202004ef  enable_group1_irq                    
0000461d  frexp                                
0000461d  frexpl                               
00006302  hw                                   
00000000  interruptVectors                     
00003351  ldexp                                
00003351  ldexpl                               
00005541  main                                 
00005695  memccpy                              
202003d2  more                                 
00004501  mpu6050_i2c_sda_unlock               
00002ce9  mpu_read_fifo_stream                 
00001319  mpu_reset_fifo                       
00002609  mspm0_i2c_read                       
00003695  mspm0_i2c_write                      
0000380d  normalizeAnalogValues                
0000273d  qsort                                
202003a0  quat                                 
00006267  reg                                  
00003351  scalbn                               
00003351  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
0000235d  sqrt                                 
0000235d  sqrtl                                
00006220  test                                 
202004e4  uwTick                               
00004e65  vsnprintf                            
00005f6d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Read_Quad                            
00001319  mpu_reset_fifo                       
00001765  dmp_read_fifo                        
00001b35  SYSCFG_DL_GPIO_init                  
00001d09  Task_Start                           
00001eb9  __aeabi_dsub                         
00001eb9  __subdf3                             
00001ec3  __adddf3                             
00001ec3  __aeabi_dadd                         
0000204b  __aeabi_idiv0                        
0000204d  No_MCU_Ganv_Sensor_Init              
000021d5  atan2                                
000021d5  atan2l                               
0000235d  sqrt                                 
0000235d  sqrtl                                
00002609  mspm0_i2c_read                       
0000273d  qsort                                
00002871  Task_Tracker                         
00002999  PID_IQ_Prosc                         
00002bdd  __aeabi_ddiv                         
00002bdd  __divdf3                             
00002ce9  mpu_read_fifo_stream                 
00002ee1  Task_Motor_PID                       
00002fd1  GROUP1_IRQHandler                    
000030b5  __aeabi_dmul                         
000030b5  __muldf3                             
00003199  DL_SYSCTL_configSYSPLL               
00003275  Get_Analog_value                     
00003351  ldexp                                
00003351  ldexpl                               
00003351  scalbn                               
00003351  scalbnl                              
00003429  __aeabi_fsub                         
00003429  __subsf3                             
00003433  __addsf3                             
00003433  __aeabi_fadd                         
00003501  Task_Init                            
000035d1  DL_Timer_initPWMMode                 
00003695  mspm0_i2c_write                      
00003759  Task_Add                             
0000380d  normalizeAnalogValues                
000038b7  __aeabi_ldiv0                        
000038b9  __udivmoddi4                         
0000395d  Motor_SetDuty                        
000039fd  SYSCFG_DL_initPower                  
00003a9d  __aeabi_fmul                         
00003a9d  __mulsf3                             
00003bb5  SYSCFG_DL_UART0_init                 
00003cbd  __aeabi_fdiv                         
00003cbd  __divsf3                             
00003d41  Motor_GetSpeed                       
00003dc1  SYSCFG_DL_Motor_PWM_init             
00003e41  __TI_decompress_lzss                 
00003ebd  Task_Serial                          
00003f35  __gedf2                              
00003f35  __gtdf2                              
00003fa9  SysTick_Handler                      
00003fb1  __aeabi_d2f                          
00003fb1  __truncdfsf2                         
00004025  No_MCU_Ganv_Sensor_Init_Frist        
00004099  MyPrintf_DMA                         
00004109  Motor_Start                          
00004175  convertAnalogToDigital               
000041e1  __cmpdf2                             
000041e1  __eqdf2                              
000041e1  __ledf2                              
000041e1  __ltdf2                              
000041e1  __nedf2                              
000042b1  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004315  SYSCFG_DL_I2C_OLED_init              
00004379  __aeabi_dcmpeq                       
0000438d  __aeabi_dcmplt                       
000043a1  __aeabi_dcmple                       
000043b5  __aeabi_dcmpge                       
000043c9  __aeabi_dcmpgt                       
000043dd  __aeabi_fcmpeq                       
000043f1  __aeabi_fcmplt                       
00004405  __aeabi_fcmple                       
00004419  __aeabi_fcmpge                       
0000442d  __aeabi_fcmpgt                       
00004441  Task_GraySensor                      
000044a1  Task_IdleFunction                    
00004501  mpu6050_i2c_sda_unlock               
00004561  DL_I2C_fillControllerTXFIFO          
000045c1  SYSCFG_DL_SYSCTL_init                
0000461d  frexp                                
0000461d  frexpl                               
00004679  SYSCFG_DL_I2C_MPU6050_init           
000046d1  Serial_Init                          
00004729  __TI_ltoa                            
000047d9  __aeabi_idiv                         
000047d9  __aeabi_idivmod                      
000049c5  DL_DMA_initChannel                   
00004a5d  SYSCFG_DL_ADC1_init                  
00004af5  __aeabi_d2iz                         
00004af5  __fixdfsi                            
00004b41  DL_UART_init                         
00004b89  adc_getValue                         
00004bd1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004c15  PID_IQ_SetParams                     
00004c9d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004ce1  __aeabi_d2uiz                        
00004ce1  __fixunsdfsi                         
00004d25  DL_ADC12_setClockConfig              
00004d65  Interrupt_Init                       
00004da5  __aeabi_uidiv                        
00004da5  __aeabi_uidivmod                     
00004de5  __aeabi_f2d                          
00004de5  __extendsfdf2                        
00004e25  atoi                                 
00004e65  vsnprintf                            
00004f5d  DL_I2C_flushControllerTXFIFO         
00004f99  Get_Anolog_Value                     
00004fd5  __aeabi_i2f                          
00004fd5  __floatsisf                          
00005011  __gesf2                              
00005011  __gtsf2                              
0000504d  __TI_auto_init_nobinit_nopinit       
00005089  __cmpsf2                             
00005089  __eqsf2                              
00005089  __lesf2                              
00005089  __ltsf2                              
00005089  __nesf2                              
000050c5  __muldsi3                            
000050ff  Get_Normalize_For_User               
00005139  __aeabi_f2iz                         
00005139  __fixsfsi                            
00005269  SYSCFG_DL_DMA_CH_RX_init             
00005299  _IQ24toF                             
00005329  SYSCFG_DL_init                       
00005381  __aeabi_i2d                          
00005381  __floatsidf                          
000053ad  PID_IQ_Init                          
00005519  SysTick_Increasment                  
00005541  main                                 
00005569  _c_int00_noargs                      
00005603  DL_I2C_setClockConfig                
0000564d  __aeabi_ui2d                         
0000564d  __floatunsidf                        
00005671  __aeabi_lmul                         
00005671  __muldi3                             
00005695  memccpy                              
000056f9  Delay                                
00005739  __aeabi_llsl                         
00005739  __ashldi3                            
000058e1  DL_Timer_setCaptCompUpdateMethod     
000058fd  DL_Timer_setClockConfig              
00005c1d  DL_Timer_setCaptureCompareOutCtl     
00005cdd  SYSCFG_DL_DMA_CH_TX_init             
00005cf5  _IQ24div                             
00005d0d  _IQ24mpy                             
00005dd7  SysGetTick                           
00005ded  __TI_zero_init_nomemset              
00005ecd  __aeabi_uldivmod                     
00005ef5  DL_UART_setClockConfig               
00005f07  TI_memcpy_small                      
00005f19  __TI_decompress_none                 
00005f4d  DL_Timer_setCaptureCompareValue      
00005f5d  SYSCFG_DL_SYSTICK_init               
00005f6d  wcslen                               
00005f7d  Get_Digtal_For_User                  
00005f8d  __aeabi_memset                       
00005f8d  __aeabi_memset4                      
00005f8d  __aeabi_memset8                      
00005fa9  TI_memset_small                      
00005fb7  SYSCFG_DL_DMA_init                   
00005fc5  Sys_GetTick                          
00005fd1  DL_Common_delayCycles                
00006051  __aeabi_errno_addr                   
00006059  __aeabi_memcpy                       
00006059  __aeabi_memcpy4                      
00006059  __aeabi_memcpy8                      
0000607f  abort                                
00006085  ADC0_IRQHandler                      
00006085  ADC1_IRQHandler                      
00006085  AES_IRQHandler                       
00006085  CANFD0_IRQHandler                    
00006085  DAC0_IRQHandler                      
00006085  DMA_IRQHandler                       
00006085  Default_Handler                      
00006085  GROUP0_IRQHandler                    
00006085  HardFault_Handler                    
00006085  I2C0_IRQHandler                      
00006085  I2C1_IRQHandler                      
00006085  NMI_Handler                          
00006085  PendSV_Handler                       
00006085  RTC_IRQHandler                       
00006085  SPI0_IRQHandler                      
00006085  SPI1_IRQHandler                      
00006085  SVC_Handler                          
00006085  TIMA0_IRQHandler                     
00006085  TIMA1_IRQHandler                     
00006085  TIMG0_IRQHandler                     
00006085  TIMG12_IRQHandler                    
00006085  TIMG6_IRQHandler                     
00006085  TIMG7_IRQHandler                     
00006085  TIMG8_IRQHandler                     
00006085  UART0_IRQHandler                     
00006085  UART1_IRQHandler                     
00006085  UART2_IRQHandler                     
00006085  UART3_IRQHandler                     
00006088  C$$EXIT                              
00006089  HOSTexit                             
00006091  Reset_Handler                        
000060a5  _system_pre_init                     
000060b0  __aeabi_ctype_table_                 
000060b0  __aeabi_ctype_table_C                
00006220  test                                 
00006267  reg                                  
00006302  hw                                   
00006398  __TI_Handler_Table_Base              
000063a4  __TI_Handler_Table_Limit             
000063ac  __TI_CINIT_Base                      
000063bc  __TI_CINIT_Limit                     
000063bc  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004c0  Data_Tracker_Input                   
202004c8  Motor                                
202004d0  Data_MotorEncoder                    
202004d4  Data_Motor_TarSpeed                  
202004d8  Data_Tracker_Offset                  
202004dc  __aeabi_errno                        
202004e0  delayTick                            
202004e4  uwTick                               
202004ec  Flag_MPU6050_Ready                   
202004ed  Gray_Digtal                          
202004ef  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[272 symbols]
