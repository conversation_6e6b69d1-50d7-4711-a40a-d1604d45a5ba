# TI_CAR1 智能小车接线说明文档

## 项目概述
本项目基于 TI MSPM0G3507 微控制器开发的智能小车系统，集成了电机控制、传感器检测、显示、通信等多种功能模块。

## 硬件平台
- **主控芯片**: MSPM0G3507 (LQFP-64封装)
- **开发板**: LP-MSPM0G3507 LaunchPad
- **工作电压**: 3.3V
- **时钟频率**: 80MHz (外部晶振40MHz，PLL倍频)

## 详细接线说明

### 1. 电源与调试接口
| 功能 | 引脚 | 包装引脚 | 说明 |
|------|------|----------|------|
| 调试时钟 | PA20 | - | SWD调试接口时钟线 |
| 调试数据 | PA19 | - | SWD调试接口数据线 |
| 外部晶振输入 | PA5 | 10 | 40MHz外部晶振输入 |
| 外部晶振输出 | PA6 | 11 | 40MHz外部晶振输出 |

### 2. 电机驱动系统 (TB6612)
#### PWM信号输出
| 功能 | 引脚 | 包装引脚 | 定时器通道 | 说明 |
|------|------|----------|------------|------|
| 左电机PWM | PA12 | 34 | TIMG0_CCP0 | 左轮速度控制 |
| 右电机PWM | PA13 | 35 | TIMG0_CCP1 | 右轮速度控制 |

#### 方向控制引脚
| 功能 | 引脚 | 包装引脚 | TB6612引脚 | 说明 |
|------|------|----------|------------|------|
| 左电机方向1 | PB6 | 58 | AIN1 | 左轮正转控制 |
| 左电机方向2 | PB7 | 59 | AIN2 | 左轮反转控制 |
| 右电机方向1 | PB25 | 27 | BIN1 | 右轮正转控制 |
| 右电机方向2 | PB24 | 23 | BIN2 | 右轮反转控制 |

#### 编码器反馈
| 功能 | 引脚 | 包装引脚 | 中断配置 | 说明 |
|------|------|----------|----------|------|
| 左轮编码器A相 | PA27 | 31 | 上升沿中断 | 左轮速度反馈 |
| 左轮编码器B相 | PA26 | 30 | 上升沿中断 | 左轮方向判断 |
| 右轮编码器A相 | PB10 | 62 | 输入检测 | 右轮速度反馈 |
| 右轮编码器B相 | PB11 | 63 | 输入检测 | 右轮方向判断 |

### 3. 传感器系统
#### MPU6050 六轴传感器 (I2C0)
| 功能 | 引脚 | 包装引脚 | I2C功能 | 说明 |
|------|------|----------|---------|------|
| 数据线 | PA0 | 1 | I2C0_SDA | 数据传输线 |
| 时钟线 | PA1 | 2 | I2C0_SCL | 时钟信号线 |
| 中断引脚 | PA30 | 37 | 下降沿中断 | 数据就绪中断 |

**I2C配置**: 400kHz快速模式，7位地址模式

#### 灰度传感器系统
| 功能 | 引脚 | 包装引脚 | 说明 |
|------|------|----------|------|
| ADC输入 | PA15 | - | 灰度传感器模拟信号输入 |
| 地址选择0 | PB18 | 15 | 传感器通道选择位0 |
| 地址选择1 | PB15 | 3 | 传感器通道选择位1 |
| 地址选择2 | PB13 | 1 | 传感器通道选择位2 |

**ADC配置**: 12位分辨率，VDDA参考电压(3.3V)

### 4. 显示系统
#### OLED显示屏 (I2C1)
| 功能 | 引脚 | 包装引脚 | I2C功能 | 说明 |
|------|------|----------|---------|------|
| 数据线 | PB3 | 16 | I2C1_SDA | OLED数据传输 |
| 时钟线 | PB2 | 15 | I2C1_SCL | OLED时钟信号 |

**I2C配置**: 400kHz快速模式，支持中断

### 5. 用户交互界面
#### LED指示灯
| 功能 | 引脚 | 包装引脚 | 初始状态 | 说明 |
|------|------|----------|----------|------|
| 板载LED | PB14 | 2 | 低电平 | 系统状态指示 |
| 红色LED | PB23 | 22 | 高电平 | 用户自定义指示 |
| 蓝色LED | PB22 | 21 | 高电平 | 用户自定义指示 |

#### 按键输入
| 功能 | 引脚 | 包装引脚 | 内部电阻 | 说明 |
|------|------|----------|----------|------|
| 按键S2 | PB21 | 20 | 上拉 | 用户按键1 |
| 按键K1 | PB19 | 16 | 上拉 | 用户按键2 |
| 按键K2 | PB20 | 19 | 上拉 | 用户按键3 |

#### 蜂鸣器
| 功能 | 引脚 | 包装引脚 | 说明 |
|------|------|----------|------|
| 蜂鸣器控制 | PA3 | 43 | 音频提示输出 |

### 6. 通信接口
#### UART0串口通信
| 功能 | 引脚 | 包装引脚 | UART功能 | 说明 |
|------|------|----------|----------|------|
| 发送 | PA10 | 21 | UART0_TX | 数据发送 |
| 接收 | PA11 | 22 | UART0_RX | 数据接收 |

**UART配置**: 
- 波特率: 115200
- 数据位: 8位
- 停止位: 1位
- 校验位: 无
- 支持DMA传输和FIFO缓冲

## 系统配置参数

### 时钟配置
- **外部晶振**: 40MHz
- **PLL配置**: 倍频10倍，分频4
- **系统时钟**: 80MHz
- **外设时钟**: 根据需要分频

### 中断优先级配置
| 中断源 | 优先级 | 说明 |
|--------|--------|------|
| SysTick | 0 | 系统时基，最高优先级 |
| UART0 | 1 | 串口通信 |
| I2C | 1 | 传感器通信 |
| GPIO | 1 | 编码器和按键中断 |

### DMA配置
| 通道 | 功能 | 说明 |
|------|------|------|
| DMA_CH0 | UART0_TX | 串口发送DMA |
| DMA_CH1 | UART0_RX | 串口接收DMA |

## 接线注意事项

### 1. 电源要求
- 确保所有模块供电电压为3.3V
- 电机驱动模块需要独立的电机电源(通常5V-12V)
- 注意电源地线的正确连接

### 2. 信号完整性
- I2C总线需要上拉电阻(通常4.7kΩ)
- 编码器信号线建议使用屏蔽线
- 长距离信号传输考虑信号衰减

### 3. 电磁兼容
- 电机驱动线路远离敏感的模拟信号
- PWM信号线使用双绞线或屏蔽线
- 在电机电源端添加滤波电容

### 4. 机械安装
- 传感器安装位置要考虑检测精度
- 编码器安装要确保同轴度
- 线缆布线要考虑机械运动范围

## 软件配置说明

### 主要功能模块
- **电机控制**: PID闭环控制，支持速度和位置控制
- **传感器处理**: MPU6050姿态解算，灰度传感器循迹
- **显示系统**: OLED实时状态显示
- **通信系统**: 串口调试和数据传输
- **任务调度**: 基于SysTick的多任务调度系统

### 编译环境
- **IDE**: Code Composer Studio (CCS)
- **SDK**: MSPM0 SDK v2.05.00.05
- **编译器**: TI Clang编译器
- **调试器**: XDS-110

## 故障排除

### 常见问题
1. **电机不转**: 检查PWM信号、方向控制信号、电源连接
2. **传感器无响应**: 检查I2C连接、上拉电阻、电源
3. **编码器计数异常**: 检查中断配置、信号线连接
4. **串口通信异常**: 检查波特率设置、TX/RX连接

### 调试建议
1. 使用示波器检查PWM信号质量
2. 使用逻辑分析仪检查I2C通信时序
3. 通过串口输出调试信息
4. 使用LED指示系统运行状态

---
*文档版本: v1.0*  
*最后更新: 2024年*  
*适用硬件: MSPM0G3507 LaunchPad*
